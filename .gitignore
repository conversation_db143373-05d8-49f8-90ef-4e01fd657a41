# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Content collections
.content-collections

# sitemap
public/sitemap*.xml
public/robots.txt

# icons
public/icons/sprite.svg
types/icons.d.ts

