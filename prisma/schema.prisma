generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters", "postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DATABASE_URL_UNPOOLED")
  extensions = [citext]
}

enum AdType {
  Banner
  Alternatives
  AlternativePage
  Tools
  ToolPage
  SelfHosted
  BlogPost
  All
}

enum ToolStatus {
  Draft
  Scheduled
  Published
}

enum StackType {
  Tool
  SaaS
  Cloud
  ETL
  Analytics
  Language
  DB
  CI
  Framework
  Hosting
  API
  Storage
  Monitoring
  Messaging
  App
  Network
}

enum ReportType {
  BrokenLink
  WrongCategory
  WrongAlternative
  Outdated
  Other
}

model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  role          String    @default("user")
  banned        Boolean?  @default(false)
  banReason     String?
  banExpires    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts Account[]
  sessions Session[]
  likes    Like[]
  tools    Tool[]
  reports  Report[]

  // Indexes
  @@index([id])
}

model Session {
  id             String   @id @default(cuid())
  token          String   @unique
  expiresAt      DateTime
  ipAddress      String?
  userAgent      String?
  impersonatedBy String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  // Indexes
  @@index([userId])
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String
  providerId            String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  // Indexes
  @@index([userId])
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Ad {
  id             String   @id @default(cuid())
  email          String
  name           String
  description    String?
  websiteUrl     String
  buttonLabel    String?
  faviconUrl     String?
  type           AdType   @default(All)
  startsAt       DateTime
  endsAt         DateTime
  sessionId      String?
  subscriptionId String?  @unique
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  alternatives Alternative[]
}

model Tool {
  id              String     @id @default(cuid())
  name            String     @db.Citext
  slug            String     @unique
  websiteUrl      String     @unique
  affiliateUrl    String?
  repositoryUrl   String     @unique
  tagline         String?
  description     String?
  content         String?
  stars           Int        @default(0)
  forks           Int        @default(0)
  score           Int        @default(0)
  faviconUrl      String?
  screenshotUrl   String?
  isFeatured      Boolean    @default(false)
  isSelfHosted    Boolean    @default(false)
  submitterName   String?
  submitterEmail  String?
  submitterNote   String?
  discountCode    String?
  discountAmount  String?
  firstCommitDate DateTime?
  lastCommitDate  DateTime?
  pageviews       Int?       @default(0)
  status          ToolStatus @default(Draft)
  publishedAt     DateTime?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  alternatives Alternative[]
  categories   Category[]
  topics       Topic[]
  stacks       Stack[]
  license      License?      @relation(fields: [licenseId], references: [id])
  licenseId    String?
  likes        Like[]
  reports      Report[]
  owner        User?         @relation(fields: [ownerId], references: [id])
  ownerId      String?

  @@index([id, slug])
  @@index([name])
  @@index([status])
  @@index([isFeatured, score])
  @@index([ownerId])
}

model Alternative {
  id             String   @id @default(cuid())
  name           String   @db.Citext
  slug           String   @unique
  description    String?
  websiteUrl     String   @unique
  faviconUrl     String?
  discountCode   String?
  discountAmount String?
  pageviews      Int?     @default(0)
  adPrice        Float?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relation
  tools Tool[]
  ad    Ad?     @relation(fields: [adId], references: [id])
  adId  String?

  // Indexes
  @@index([slug])
  @@index([adId])
}

model Category {
  id          String   @id @default(cuid())
  name        String   @db.Citext
  slug        String   @unique
  fullPath    String   @unique @default("")
  label       String?
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tools         Tool[]
  parent        Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  parentId      String?
  subcategories Category[] @relation("CategoryHierarchy")

  // Indexes
  @@index([slug])
  @@index([parentId])
}

model Topic {
  slug      String   @id @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relation
  tools Tool[]

  // Indexes
  @@index([slug])
}

model License {
  id        String   @id @default(cuid())
  name      String   @unique @db.Citext
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tools Tool[]

  // Indexes
  @@index([slug])
}

model Stack {
  id          String    @id @default(cuid())
  name        String    @db.Citext
  slug        String    @unique
  type        StackType @default(Language)
  description String?
  website     String?
  faviconUrl  String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tools Tool[]

  // Indexes
  @@index([slug])
}

model Report {
  id        String     @id @default(cuid())
  type      ReportType
  message   String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String?
  tool   Tool?   @relation(fields: [toolId], references: [id], onDelete: Cascade)
  toolId String?

  // Indexes
  @@index([toolId])
}

model Like {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String
  tool   Tool   @relation(fields: [toolId], references: [id], onDelete: Cascade)
  toolId String

  // Indexes
  @@unique([userId, toolId])
  @@index([userId])
  @@index([toolId])
}
