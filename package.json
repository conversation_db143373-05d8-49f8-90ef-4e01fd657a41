{"name": "openalternative", "private": true, "type": "module", "scripts": {"email": "cp .env ../../node_modules/react-email/dist/preview && email dev", "dev": "next dev -p 5173 --turbopack", "start": "next start -p 5173", "build": "next build --turbopack", "lint": "bun biome check --write .", "format": "bun biome format --write .", "typecheck": "tsc --noEmit", "icons": "bun run ./scripts/build-icons.ts", "db:generate": "prisma generate --no-hints", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:push": "prisma db push --skip-generate", "db:reset": "prisma migrate reset", "prebuild": "bun run icons", "postbuild": "next-sitemap", "postinstall": "bun run db:generate"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/react": "^1.2.12", "@atproto/api": "^0.15.16", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/lib-storage": "^3.832.0", "@content-collections/core": "0.7.3", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "0.2.4", "@hookform/resolvers": "^3.10.0", "@mantine/hooks": "^8.1.1", "@number-flow/react": "^0.5.10", "@octokit/graphql": "^9.0.1", "@primoui/utils": "^1.1.8", "@prisma/client": "6.5.0", "@react-email/components": "0.0.28", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-table": "^8.21.3", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "ai": "^4.3.16", "better-auth": "^1.2.9", "change-case": "^5.4.4", "cmdk": "^1.1.1", "cva": "beta", "date-fns": "^4.1.0", "inngest": "^3.39.2", "meilisearch": "^0.50.0", "motion": "^12.18.1", "next": "canary", "nuqs": "^2.4.3", "plur": "^5.1.0", "posthog-js": "^1.255.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-email": "^4.0.16", "react-hook-form": "^7.58.1", "react-markdown": "9.0.1", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "resend": "^4.6.0", "rss": "^1.2.2", "satori": "^0.13.2", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "wretch": "^2.11.0", "zod": "^3.25.67", "zsa": "^0.6.0", "zsa-react": "^0.2.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.32", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/rss": "^0.0.32", "fs-extra": "^11.3.0", "glob": "^11.0.3", "next-sitemap": "^4.2.3", "node-html-parser": "^7.0.1", "postcss": "^8.5.6", "prisma": "6.5.0", "schema-dts": "^1.1.5", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}, "packageManager": "bun@1.2.2"}