{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "files": {"ignore": ["**/.next"]}, "organizeImports": {"enabled": true}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2, "formatWithErrors": true}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded", "arrowParentheses": "asNeeded", "bracketSpacing": true, "trailingCommas": "all"}}, "json": {"parser": {"allowComments": false, "allowTrailingCommas": true}, "formatter": {"trailingCommas": "none"}}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off", "noConfusingLabels": "off", "noShadowRestrictedNames": "off"}, "correctness": {"useExhaustiveDependencies": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "style": {"noNonNullAssertion": "off"}, "a11y": {"noBlankTarget": "off", "useAnchorContent": "off", "useValidAnchor": "off", "useAltText": "off"}}}}