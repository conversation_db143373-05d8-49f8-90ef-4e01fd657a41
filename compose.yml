services:
  postgres:
    image: postgres:latest
    restart: unless-stopped
    environment:
      TZ: UTC
      PGTZ: UTC
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    ports:
      - "5432:5432"
    volumes:
      - openalternative-db-data:/var/lib/postgresql/data/
    deploy:
      resources:
        limits:
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 5s
      timeout: 5s
      retries: 5

  pgbouncer:
    image: bitnami/pgbouncer:latest
    restart: unless-stopped
    environment:
      POSTGRESQL_HOST: postgres
      POSTGRESQL_PORT: 5432
      POSTGRESQL_USERNAME: ${DB_USER}
      POSTGRESQL_PASSWORD: ${DB_PASSWORD}
      PGBOUNCER_DATABASE: ${DB_NAME}
      PGBOUNCER_PORT: 6543
      PGBOUNCER_BIND_ADDRESS: 0.0.0.0
    ports:
      - "6543:6543"
    depends_on:
      postgres:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 512MB

volumes:
  openalternative-db-data:
    driver: local
