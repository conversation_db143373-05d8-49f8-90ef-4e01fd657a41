{"compilerOptions": {"allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "lib": ["ESNext", "DOM", "DOM.Iterable"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "moduleResolution": "<PERSON><PERSON><PERSON>", "incremental": true, "plugins": [{"name": "next"}], "paths": {"~/*": ["./*"], "content-collections": ["./.content-collections/generated"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}